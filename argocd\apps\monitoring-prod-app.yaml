# argocd/apps/monitoring-prod-app.yaml
apiVersion: argoproj.io/v1alpha1
kind: Application

metadata:
  # 在 Argo CD UI 中，这个应用将被命名为 'monitoring-prod'
  name: monitoring-prod
  # 这个 Application 资源本身将存放在 'argocd' 命名空间中
  namespace: argocd

spec:
  # 使用默认项目
  project: default

  # 定义配置来源 (您的 Git 仓库)
  source:
    # 请将这里的 URL 替换为您自己的 GitOps 仓库地址！
    repoURL: https://github.com/liuc-c/k3s-cluster-argocd.git

    # 指向我们为生产环境创建的 Kustomize overlay 目录
    path: apps/monitoring/overlays/prod

    # 跟踪默认分支 (例如 main 或 master) 的最新变更
    targetRevision: HEAD

  # 定义部署目标 (您的 K3s 集群)
  destination:
    # 这个固定值意味着“部署到当前集群”
    name: in-cluster
    namespace: monitoring

  # 定义同步策略，实现完全的自动化 GitOps
  syncPolicy:
    automated:
      # 自动删除不再存在于 Git 中的资源，保持环境干净
      prune: true
      # 自动修复手动更改，确保 Git 是唯一的事实来源
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
      - PruneLast=true
      - ApplyOutOfSyncOnly=true
      - ServerSideApply=true
    retry:
      limit: 2
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m0s

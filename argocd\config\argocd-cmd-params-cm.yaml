# argocd/config/argocd-cmd-params-cm.yaml
# 配置 ArgoCD 组件的超时和性能参数
apiVersion: v1
kind: ConfigMap
metadata:
  name: argocd-cmd-params-cm
  namespace: argocd
  labels:
    app.kubernetes.io/name: argocd-cmd-params-cm
    app.kubernetes.io/part-of: argocd
data:
  # Application Controller 配置
  controller.operation.processors: "10"
  controller.status.processors: "20"
  controller.self.heal.timeout.seconds: "5"
  controller.repo.server.timeout.seconds: "120"  # 增加 repo server 超时时间
  
  # Repository Server 配置
  reposerver.parallelism.limit: "10"
  reposerver.timeout: "120"  # 增加 repo server 超时时间
  
  # Server 配置
  server.insecure: "false"
  server.grpc.keepalive.time: "30s"
  server.grpc.keepalive.timeout: "5s"
  
  # 集群连接配置
  application.instanceLabelKey: "argocd.argoproj.io/instance"
  
  # 增加 API 请求超时
  controller.kubectl.parallelism.limit: "10"
